import XLSX from 'xlsx';

// Sample data with all columns needed for testing different tip calculation methods
const sampleData = [
  {
    'Employee Name': '<PERSON>',
    'Hours Worked': 8,
    'Sales Generated': 1200,
    Role: 'Server',
    Shift: 'dinner',
    'Performance Rating': 4.5,
    Section: 'A',
    'Service Rating': 4.8,
    'Employee Level Points': 10,
    'Role Points': 15,
    'Work Hours Points': 8,
    'Shift Period Points': 12,
  },
  {
    'Employee Name': '<PERSON>',
    'Hours Worked': 6,
    'Sales Generated': 900,
    Role: 'Server',
    Shift: 'lunch',
    'Performance Rating': 4.2,
    Section: 'B',
    'Service Rating': 4.6,
    'Employee Level Points': 8,
    'Role Points': 15,
    'Work Hours Points': 6,
    'Shift Period Points': 8,
  },
  {
    'Employee Name': '<PERSON>',
    'Hours Worked': 10,
    'Sales Generated': 1500,
    Role: 'Manager',
    Shift: 'dinner',
    'Performance Rating': 4.8,
    Section: 'A',
    'Service Rating': 4.9,
    'Employee Level Points': 15,
    'Role Points': 25,
    'Work Hours Points': 10,
    'Shift Period Points': 12,
  },
  {
    'Employee Name': '<PERSON>',
    'Hours Worked': 7,
    'Sales Generated': 800,
    Role: 'Bartender',
    Shift: 'dinner',
    'Performance Rating': 4.3,
    Section: 'Bar',
    'Service Rating': 4.7,
    'Employee Level Points': 12,
    'Role Points': 20,
    'Work Hours Points': 7,
    'Shift Period Points': 12,
  },
  {
    'Employee Name': '<PERSON>',
    'Hours Worked': 5,
    'Sales Generated': 600,
    Role: 'Host',
    Shift: 'morning',
    'Performance Rating': 4.0,
    Section: 'Front',
    'Service Rating': 4.4,
    'Employee Level Points': 6,
    'Role Points': 10,
    'Work Hours Points': 5,
    'Shift Period Points': 6,
  },
  {
    'Employee Name': 'Lisa Martinez',
    'Hours Worked': 9,
    'Sales Generated': 1350,
    Role: 'Server',
    Shift: 'dinner',
    'Performance Rating': 4.6,
    Section: 'A',
    'Service Rating': 4.8,
    'Employee Level Points': 14,
    'Role Points': 15,
    'Work Hours Points': 9,
    'Shift Period Points': 12,
  },
  {
    'Employee Name': 'Robert Garcia',
    'Hours Worked': 6.5,
    'Sales Generated': 750,
    Role: 'Server',
    Shift: 'lunch',
    'Performance Rating': 3.9,
    Section: 'B',
    'Service Rating': 4.3,
    'Employee Level Points': 9,
    'Role Points': 15,
    'Work Hours Points': 6.5,
    'Shift Period Points': 8,
  },
  {
    'Employee Name': 'Jennifer Lee',
    'Hours Worked': 8.5,
    'Sales Generated': 1100,
    Role: 'Bartender',
    Shift: 'dinner',
    'Performance Rating': 4.4,
    Section: 'Bar',
    'Service Rating': 4.6,
    'Employee Level Points': 11,
    'Role Points': 20,
    'Work Hours Points': 8.5,
    'Shift Period Points': 12,
  },
  {
    'Employee Name': 'Kevin Anderson',
    'Hours Worked': 4,
    'Sales Generated': 400,
    Role: 'Busser',
    Shift: 'morning',
    'Performance Rating': 3.8,
    Section: 'C',
    'Service Rating': 4.2,
    'Employee Level Points': 5,
    'Role Points': 8,
    'Work Hours Points': 4,
    'Shift Period Points': 6,
  },
  {
    'Employee Name': 'Amanda Taylor',
    'Hours Worked': 7.5,
    'Sales Generated': 950,
    Role: 'Server',
    Shift: 'lunch',
    'Performance Rating': 4.1,
    Section: 'C',
    'Service Rating': 4.5,
    'Employee Level Points': 10,
    'Role Points': 15,
    'Work Hours Points': 7.5,
    'Shift Period Points': 8,
  },
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Convert the data to a worksheet
const ws = XLSX.utils.json_to_sheet(sampleData);

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, 'Employee Data');

// Write the file
XLSX.writeFile(wb, 'sample-tip-calculation-data.xlsx');

console.log('Excel file generated successfully: sample-tip-calculation-data.xlsx');
console.log('This file contains sample data to test all tip calculation methods:');
console.log('- Basic columns: Employee Name, Hours Worked, Sales Generated');
console.log('- Role-based: Role column with different positions');
console.log('- Shift-based: Shift column with morning/lunch/dinner');
console.log('- Performance-based: Performance Rating column');
console.log('- Section-based: Section column for different areas');
console.log('- Service rating-based: Service Rating column');
console.log('- Points-based: Multiple point factor columns for testing point systems');
