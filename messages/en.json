{"common": {"welcome": "Welcome to the Restaurant Tip Calculator", "language": "Language", "ok": "OK", "processing": "Processing...", "error": "Error", "success": "Success"}, "home": {"title": "Restaurant Tip Calculator", "subtitle": "Upload your employee data, select a calculation method, and get your tip distributions.", "roleMultipliersNote": "Set multipliers for each role to adjust tip distribution. Example: Manager = 1.5, Server = 1.0, Busser = 0.8, Host = 0.7. You can also set minimum and maximum tips for each role.", "fileUpload": {"title": "Upload Employee Data (Excel .xlsx/.xls)", "clearFile": "Clear File", "selectedFile": "Selected file:", "noFileSelected": "No file selected.", "emptyFile": "The uploaded Excel file is empty or could not be parsed.", "invalidFormat": "Failed to read Excel file. Please ensure it is a valid .xlsx or .xls format.", "fileReadError": "Error reading file. Please try again."}, "calculations": {"title": "Choose Tip Calculation Method", "selectFormula": "Select Formula:", "formulaOptions": "Formula Specific Options:", "basicMethods": "Basic Methods", "proportionalByHours": "Tip Pooling: Proportional by Hours Worked", "fixedAmount": "Tip Pooling: Fixed Amount per Employee", "pointsSystem": "Tip Pooling: Points System", "directPercentage": "Tip Splitting: Direct Percentage of Sales", "enhancedMethods": "Enhanced Methods", "weightedDistribution": "Weighted Distribution (Hours + Sales + Performance)", "roleBasedTips": "Role-Based Tips (Different rates by position)", "shiftBasedTips": "Shift-Based Tips (Different rates by time)", "performanceBasedTips": "Performance-Based Tips (Bonuses for high performers)", "sectionBasedTips": "Section-Based Tips (Separate pools by area)", "serviceRatingBasedTips": "Service Rating-Based Tips (Customer feedback)", "helpText": "Help Text"}, "options": {"tipPoolPercentage": "Tip Pool Percentage of Total Sales (e.g., 0.05 for 5%):", "fixedAmountPerEmployee": "Fixed Amount per Employee:", "totalTipPool": "Total Tip Pool (Optional, will calculate from sales if not provided):", "pointFactorColumns": "Select Columns for Point Factors (Hold Ctrl/Cmd to select multiple):", "pointFactorColumnsNote": "These columns should contain numeric values representing points.", "directPercentage": "Percentage of Individual Sales (e.g., 0.10 for 10%):", "hoursWeight": "Hours Weight:", "salesWeight": "Sales Weight:", "performanceWeight": "Performance Weight:", "performanceColumn": "Performance Column (optional):", "selectPerformanceColumn": "Select Performance Column", "weightsNote": "Weights should sum to 1.0. Combines hours worked, sales generated, and optional performance metrics.", "roleColumn": "Role Column:", "selectRoleColumn": "Select Role Column", "roleMultipliersNote": "Default role multipliers: Manager (1.5x), Server (1.0x), <PERSON><PERSON> (0.8x), Host (0.7x)", "shiftColumn": "Shift Column:", "selectShiftColumn": "Select Shift Column", "shiftMultipliersNote": "Default shift multipliers: Morning (0.8x), Lunch (1.0x), Dinner (1.3x), Late (1.1x)", "performanceMetric": "Performance Metric:", "salesGenerated": "Sales Generated", "salesPerHour": "Sales per Hour", "hoursWorked": "Hours Worked", "customColumn": "Custom Column", "customPerformanceColumn": "Custom Performance Column:", "selectPerformanceColumnCustom": "Select Performance Column", "performanceBonusNote": "Adds bonuses: Excellent performance (≥$1000) gets 20% bonus, Good performance (≥$500) gets 10% bonus", "sectionColumn": "Section Column:", "selectSectionColumn": "Select Section Column", "sectionAllocationsNote": "Default allocations: Dining Room (60%), Bar (30%), Kitchen (10%)", "serviceRatingColumn": "Service Rating Column:", "selectRatingColumn": "Select Rating Column", "ratingWeight": "Rating Weight (0-1):", "minimumRating": "Minimum Rating:", "serviceRatingNote": "Combines hours worked with customer service ratings. Only employees meeting minimum rating qualify.", "uploadExcelNote": "Upload Excel to see columns"}, "constraints": {"title": "Global Constraints (Optional)", "minimumTipPerEmployee": "Minimum Tip per Employee ($):", "maximumTipPerEmployee": "Maximum Tip per Employee ($):", "minimumTipPerHour": "Minimum Tip per Hour ($):", "noMinimum": "0 = no minimum", "noMaximum": "0 = no maximum", "constraintsNote": "These constraints apply to all calculation methods and ensure fair minimum/maximum tip amounts."}, "results": {"title": "Calculated Tips", "showAnalytics": "Show Analytics", "hideAnalytics": "Hide Analytics", "exportToExcel": "Export to Excel", "analytics": {"title": "📊 Calculation Analytics", "totalTipPool": "Total Tip Pool", "averageTip": "Average Tip", "medianTip": "Median Tip", "tipRange": "Tip Range", "fairnessAnalysis": "🎯 Fairness Analysis", "equalityScore": "Equality Score (Gini Coefficient):", "tipPerHourVariance": "Tip Per Hour Variance:", "veryFair": "Very Fair", "moderatelyFair": "Moderately Fair", "lessFair": "Less Fair", "distributionBreakdown": "📈 Distribution Breakdown", "byRole": "By Role", "byShift": "By Shift", "dataQualityWarnings": "⚠️ Data Quality Warnings"}}, "instructions": {"title": "Upload an Excel file with columns like \"Employee Name\", \"Hours Worked\", and \"Sales Generated\" to get started.", "description": "Upload your employee data, select a calculation method, and get your tip distributions in just a few simple steps.", "step1": "Upload an Excel file with employee data including names, hours worked, and sales generated.", "step2": "Choose from 10 different tip calculation methods based on your restaurant's needs.", "step3": "Review detailed analytics and export results to Excel for record-keeping.", "pointsSystemNote": "For the \"Points System\" method, ensure your Excel includes columns with point values (e.g., \"Level Points\", \"Role Points\").", "subtitle": "This is a foundational component for your Next.js 15 SAAS."}, "errors": {"noDataToExport": "No data to export. Please upload an Excel file and calculate tips first.", "calculationError": "Calculation error:", "unknownError": "An unknown error occurred.", "couldNotCalculate": "Could not calculate tips. Please check your Excel file headers and selected formula options.", "pointsSystemMissingColumns": "Points System: Please select at least one column for point factors.", "pointsSystemMissingFiles": "Points System: The following columns are missing in the uploaded file:", "exportSuccess": "Successfully exported data to Excel!", "exportFailed": "Failed to export data to Excel. Please try again."}}}