{"common": {"welcome": "Bienvenue au Calculateur de Pourboires de Restaurant", "language": "<PERSON><PERSON>", "ok": "OK", "processing": "En cours de traitement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès"}, "home": {"title": "Calculateur de Pourboires de Restaurant", "subtitle": "Téléchargez vos données d'employés, sélectionnez une méthode de calcul et obtenez vos distributions de pourboires.", "roleMultipliersNote": "Définissez des multiplicateurs pour chaque rôle pour ajuster la distribution des pourboires. Exemple : Manager = 1.5, Ser<PERSON>ur = 1.0, Aide-serveur = 0.8, <PERSON>ô<PERSON> = 0.7. Vous pouvez également définir des pourboires minimum et maximum pour chaque rôle.", "fileUpload": {"title": "Télécharger les Données des Employés (Excel .xlsx/.xls)", "clearFile": "<PERSON><PERSON><PERSON><PERSON>", "selectedFile": "<PERSON><PERSON>er sélectionné :", "noFileSelected": "<PERSON><PERSON>n fichier sélectionn<PERSON>.", "emptyFile": "Le fichier Excel téléchargé est vide ou n'a pas pu être analysé.", "invalidFormat": "Échec de lecture du fichier Excel. Veuillez vous assurer qu'il s'agit d'un format .xlsx ou .xls valide.", "fileReadError": "Erreur de lecture du fichier. Veuillez réessayer."}, "calculations": {"title": "Choisir la Méthode de Calcul de Pourboires", "selectFormula": "Sélectionner la Formule :", "formulaOptions": "Options Spécifiques à la Formule :", "basicMethods": "Méthodes de Base", "proportionalByHours": "Mutualisation de Pourboires : Proportionnel aux Heures Travaillées", "fixedAmount": "Mutualisation de Pourboires : Montant Fixe par Employé", "pointsSystem": "Mutualisation de Pourboires : Système de Points", "directPercentage": "Répartition de Pourboires : Pourcentage Direct des Ventes", "enhancedMethods": "Méthodes <PERSON>nc<PERSON>", "weightedDistribution": "Distribution Pondérée (Heures + Ventes + Performance)", "roleBasedTips": "Pourboires Basés sur le Rôle (Taux différents par poste)", "shiftBasedTips": "Pourboires Basés sur l'Équipe (Taux différents par horaire)", "performanceBasedTips": "Pourboires Basés sur la Performance (Bonus pour les plus performants)", "sectionBasedTips": "Pourboires Basés sur la Section (Pools séparés par zone)", "serviceRatingBasedTips": "Pourboires Basés sur l'Évaluation du Service (Commentaires clients)", "helpText": "Aide Texte"}, "options": {"tipPoolPercentage": "Pourcentage du Pool de Pourboires sur les Ventes Totales (ex. 0.05 pour 5%) :", "fixedAmountPerEmployee": "Montant Fixe par Employé :", "totalTipPool": "Pool de Pourboires Total (Optionnel, sera calculé à partir des ventes si non fourni) :", "pointFactorColumns": "Sélectionner les Colonnes pour les Facteurs de Points (Maintenez Ctrl/Cmd pour sélectionner plusieurs) :", "pointFactorColumnsNote": "Ces colonnes doivent contenir des valeurs numériques représentant les points.", "directPercentage": "Pourcentage des Ventes Individuelles (ex. 0.10 pour 10%) :", "hoursWeight": "Poids des Heures :", "salesWeight": "Poids des Ventes :", "performanceWeight": "Poids de la Performance :", "performanceColumn": "Colonne de Performance (optionnel) :", "selectPerformanceColumn": "Sélectionner la Colonne de Performance", "weightsNote": "Les poids doivent totaliser 1.0. Combine les heures travaillées, les ventes générées et les métriques de performance optionnelles.", "roleColumn": "Colonne des Rôles :", "selectRoleColumn": "Sélectionner la Colonne des Rôles", "roleMultipliersNote": "Multiplicateurs de rôle par défaut : <PERSON> (1.5x), <PERSON><PERSON><PERSON> (1.0x), <PERSON><PERSON><PERSON><PERSON><PERSON> (0.8x), <PERSON><PERSON><PERSON> (0.7x)", "shiftColumn": "Colonne des Équipes :", "selectShiftColumn": "Sélectionner la Colonne des Équipes", "shiftMultipliersNote": "Multiplicateurs d'équipe par dé<PERSON><PERSON> : <PERSON><PERSON> (0.8x), <PERSON><PERSON><PERSON><PERSON><PERSON> (1.0x), <PERSON><PERSON><PERSON> (1.3x), <PERSON><PERSON> (1.1x)", "performanceMetric": "Métrique de Performance :", "salesGenerated": "<PERSON><PERSON><PERSON>", "salesPerHour": "Ventes par He<PERSON>", "hoursWorked": "Heures Travaillées", "customColumn": "<PERSON><PERSON><PERSON>", "customPerformanceColumn": "Colonne de Performance Personnalisée :", "selectPerformanceColumnCustom": "Sélectionner la Colonne de Performance", "performanceBonusNote": "Ajoute des bonus : Performance excellente (≥1000$) obtient 20% de bonus, <PERSON><PERSON> performance (≥500$) obtient 10% de bonus", "sectionColumn": "Colonne des Sections :", "selectSectionColumn": "Sélectionner la Colonne des Sections", "sectionAllocationsNote": "Allocations par défaut : <PERSON> à manger (60%), <PERSON> (30%), <PERSON><PERSON><PERSON><PERSON> (10%)", "serviceRatingColumn": "Colonne d'Évaluation du Service :", "selectRatingColumn": "Sélectionner la Colonne d'Évaluation", "ratingWeight": "Poids de l'Évaluation (0-1) :", "minimumRating": "Évaluation Minimale :", "serviceRatingNote": "Combine les heures travaillées avec les évaluations du service client. Seuls les employés atteignant l'évaluation minimale sont qualifiés.", "uploadExcelNote": "Téléchargez Excel pour voir les colonnes"}, "constraints": {"title": "Contraintes Globales (Optionnel)", "minimumTipPerEmployee": "Pourboire Minimum par Employé ($) :", "maximumTipPerEmployee": "Pourboire Maximum par Employé ($) :", "minimumTipPerHour": "Pourboire Minimum par Heure ($) :", "noMinimum": "0 = pas de minimum", "noMaximum": "0 = pas de maximum", "constraintsNote": "Ces contraintes s'appliquent à toutes les méthodes de calcul et garantissent des montants de pourboires minimum/maximum équitables."}, "results": {"title": "Pourboires Calculés", "showAnalytics": "Afficher les Analyses", "hideAnalytics": "Masquer les Analyses", "exportToExcel": "Exporter vers Excel", "analytics": {"title": "📊 Ana<PERSON><PERSON> de Calcul", "totalTipPool": "Pool de Pourboires Total", "averageTip": "<PERSON><PERSON><PERSON><PERSON>", "medianTip": "<PERSON><PERSON><PERSON><PERSON>", "tipRange": "Plage de Pourboires", "fairnessAnalysis": "🎯 Ana<PERSON><PERSON> d'<PERSON>", "equalityScore": "Score d'Égalité (Coefficient de Gini) :", "tipPerHourVariance": "<PERSON><PERSON><PERSON> de Pourboire par Heure :", "veryFair": "<PERSON><PERSON>ès <PERSON>", "moderatelyFair": "Modérément Équitable", "lessFair": "Moins Équi<PERSON>", "distributionBreakdown": "📈 Répartition de Distribution", "byRole": "<PERSON><PERSON>", "byShift": "<PERSON><PERSON><PERSON>", "dataQualityWarnings": "⚠️ Avertissements de Qualité des Données"}}, "instructions": {"title": "Téléchargez un fichier Excel avec des colonnes comme \"Nom de l'Employé\", \"Heures Travaillées\" et \"Ventes Générées\" pour commencer.", "description": "Téléchargez vos données d'employés, sélectionnez une méthode de calcul et obtenez vos distributions de pourboires en quelques étapes simples.", "step1": "Téléchargez un fichier Excel avec les données des employés incluant les noms, heures travaillées et ventes générées.", "step2": "Choisissez parmi 10 méthodes différentes de calcul de pourboires selon les besoins de votre restaurant.", "step3": "Consultez les analyses détaillées et exportez les résultats vers Excel pour la tenue de dossiers.", "pointsSystemNote": "Pour la méthode \"Système de Points\", assurez-vous que votre Excel inclut des colonnes avec des valeurs de points (ex. \"Points de Niveau\", \"Points de Rôle\").", "subtitle": "Ceci est un composant fondamental pour votre SAAS Next.js 15."}, "errors": {"noDataToExport": "Aucune donnée à exporter. Veuillez télécharger un fichier Excel et calculer les pourboires d'abord.", "calculationError": "Erreur de calcul :", "unknownError": "Une erreur inconnue s'est produite.", "couldNotCalculate": "Impossible de calculer les pourboires. Veuillez vérifier les en-têtes de votre fichier Excel et les options de formule sélectionnées.", "pointsSystemMissingColumns": "Système de Points : <PERSON><PERSON><PERSON><PERSON> sélectionner au moins une colonne pour les facteurs de points.", "pointsSystemMissingFiles": "Système de Points : Les colonnes suivantes sont manquantes dans le fichier téléchargé :", "exportSuccess": "Données exportées vers Excel avec succès !", "exportFailed": "Échec de l'exportation des données vers Excel. Veuillez réessayer."}}}