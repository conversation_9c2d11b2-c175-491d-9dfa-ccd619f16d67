import createNextIntlPlugin from 'next-intl/plugin';
import type { NextConfig } from 'next';

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  async redirects() {
    return [
      {
        source: '/',
        destination: '/en',
        permanent: false, // Use false for temporary redirects (e.g., during development)
      },
    ];
  },
  /* config options here */
};

export default withNextIntl(nextConfig);
