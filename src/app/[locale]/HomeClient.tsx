'use client';

import React, { useState, useRef, ChangeEvent, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import LanguageSwitcher from '../../components/LanguageSwitcher';
import CustomModal from '../../components/CustomModal';
import FileUpload from '../../components/FileUpload';
import FormulaSelector from '../../components/FormulaSelector';
import GlobalConstraints from '../../components/GlobalConstraints';
import ResultsTable from '../../components/ResultsTable';
import AnalyticsPanel from '../../components/AnalyticsPanel';
import InstructionsPanel from '../../components/InstructionsPanel';
import {
  EmployeeData,
  CalculatedTipResult,
  FormulaOptions,
  TipCalculationSummary,
  FairnessMetrics,
  DataValidationResult
} from '../../lib/utils/tipCalculationHelper';
import {
  processExcelFile,
  calculateTipsForEmployees,
  exportTipsToExcel,
  getDefaultFormulaOptions
} from '../../lib/utils/fileProcessingUtils';
import tipCalculationHelperDescriptions from '../../lib/utils/tipCalculationHelperDescriptions.json';
import { FORMULA_OPTIONS } from '../../constants/home';

interface TipCalculationMethods {
  [key: string]: {
    description: string;
    usage: {
      configuration: Record<string, string>;
      globalConstraints: string;
    };
  };
}

const tipCalculationMethods: TipCalculationMethods = tipCalculationHelperDescriptions.methods;

export default function HomeClient() {
  const t = useTranslations('home');
  
  const [excelData, setExcelData] = useState<EmployeeData[]>([]);
  const [calculatedTips, setCalculatedTips] = useState<CalculatedTipResult[]>([]);
  const [fileName, setFileName] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showModal, setShowModal] = useState<boolean>(false);
  const [modalMessage, setModalMessage] = useState<string>('');

  const [calculationSummary, setCalculationSummary] = useState<TipCalculationSummary | null>(null);
  const [fairnessMetrics, setFairnessMetrics] = useState<FairnessMetrics | null>(null);
  const [dataValidation, setDataValidation] = useState<DataValidationResult | null>(null);
  const [showAnalytics, setShowAnalytics] = useState<boolean>(false);

  const [selectedFormula, setSelectedFormula] = useState<string>('proportionalByHours');
  const [formulaOptions, setFormulaOptions] = useState<FormulaOptions>(FORMULA_OPTIONS);
  const [availableExcelColumns, setAvailableExcelColumns] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [selectedFormulaDescription, setSelectedFormulaDescription] = useState<string>('');

  useEffect(() => {
    if ( selectedFormula && tipCalculationMethods[ selectedFormula ] )
    {
      const methodDetails = tipCalculationMethods[selectedFormula];
      setSelectedFormulaDescription(`${methodDetails.description}\n\nUsage:\n${JSON.stringify(methodDetails.usage, null, 2)}`);
    } else {
      setSelectedFormulaDescription('');
    }
  }, [selectedFormula]);

  const handleShowModal = (message: string) => {
    setModalMessage(message);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setModalMessage('');
  };

  const handleClearFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setExcelData([]);
    setCalculatedTips([]);
    setFileName('');
    setErrorMessage('');
    setAvailableExcelColumns([]);
    setFormulaOptions(getDefaultFormulaOptions());
    setSelectedFormula('proportionalByHours');
  };

  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      handleClearFile();
      setErrorMessage(t('fileUpload.noFileSelected'));
      return;
    }

    setFileName(file.name);
    setIsProcessing(true);
    setErrorMessage('');
    setExcelData([]);
    setCalculatedTips([]);
    setAvailableExcelColumns([]);

    try {
      const result = await processExcelFile(file);
      
      if (result.success && result.data && result.columns) {
        setExcelData(result.data);
        setAvailableExcelColumns(result.columns);
        
        const calculationResult = calculateTipsForEmployees(
          result.data, 
          selectedFormula, 
          formulaOptions, 
          result.columns
        );
        
        if (calculationResult.error) {
          setErrorMessage(calculationResult.error);
          handleShowModal(calculationResult.error);
        } else {
          setCalculatedTips(calculationResult.results);
          setCalculationSummary(calculationResult.summary);
          setFairnessMetrics(calculationResult.fairness);
          setDataValidation(calculationResult.validation);

          if (calculationResult.validation?.warnings.length) {
            console.warn('Data validation warnings:', calculationResult.validation.warnings);
          }
        }
      } else {
        setErrorMessage(result.error || t('fileUpload.invalidFormat'));
        handleShowModal(result.error || t('fileUpload.invalidFormat'));
        handleClearFile();
      }
    } catch (error) {
      console.error('Error processing file:', error);
      setErrorMessage(t('fileUpload.fileReadError'));
      handleShowModal(t('fileUpload.fileReadError'));
      handleClearFile();
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCalculateTips = useCallback((data: EmployeeData[], formula: string, options: FormulaOptions) => {
    if (data.length === 0) {
      setCalculatedTips([]);
      setCalculationSummary(null);
      setFairnessMetrics(null);
      setDataValidation(null);
      return;
    }

    setErrorMessage('');
    
    const calculationResult = calculateTipsForEmployees(data, formula, options, availableExcelColumns);
    
    if (calculationResult.error) {
      setErrorMessage(calculationResult.error);
      handleShowModal(calculationResult.error);
      setCalculatedTips([]);
      setCalculationSummary(null);
      setFairnessMetrics(null);
      setDataValidation(null);
    } else {
      setCalculatedTips(calculationResult.results);
      setCalculationSummary(calculationResult.summary);
      setFairnessMetrics(calculationResult.fairness);
      setDataValidation(calculationResult.validation);

      if (calculationResult.validation?.warnings.length) {
        console.warn('Data validation warnings:', calculationResult.validation.warnings);
      }

      if (calculationResult.results.length === 0 && data.length > 0) {
        setErrorMessage(t('errors.couldNotCalculate'));
        handleShowModal(t('errors.couldNotCalculate'));
      }
    }
  }, [availableExcelColumns, t]);

  useEffect(() => {
    if (excelData.length > 0) {
      handleCalculateTips(excelData, selectedFormula, formulaOptions);
    }
  }, [selectedFormula, formulaOptions, excelData, handleCalculateTips]);

  const exportToExcel = () => {
    if (calculatedTips.length === 0) {
      handleShowModal(t('errors.noDataToExport'));
      return;
    }

    setIsProcessing(true);
    
    const success = exportTipsToExcel(calculatedTips, `Calculated_Tips_${Date.now()}.xlsx`);
    
    if (success) {
      handleShowModal(t('errors.exportSuccess'));
    } else {
      setErrorMessage(t('errors.exportFailed'));
      handleShowModal(t('errors.exportFailed'));
    }
    
    setIsProcessing(false);
  };

  const handleFormulaOptionChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormulaOptions(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) : value
    }));
  };

  const handlePointFactorColumnsChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
    setFormulaOptions(prev => ({
      ...prev,
      pointFactorColumns: selectedOptions
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 font-sans text-gray-900 p-4 md:p-8 flex flex-col items-center">
      <CustomModal message={modalMessage} onClose={handleCloseModal} show={showModal} />

      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 w-full max-w-5xl border border-gray-200">
        {/* Language Switcher */}
        <div className="flex justify-end mb-4">
          <LanguageSwitcher />
        </div>
        
        <h1 className="text-3xl md:text-4xl font-extrabold text-center text-blue-700 mb-6">
          {t('title')}
        </h1>
        <p className="text-center text-gray-600 mb-8">
          {t('subtitle')}
        </p>

        {/* File Upload Section */}
        <FileUpload
          fileName={fileName}
          isProcessing={isProcessing}
          errorMessage={errorMessage}
          onFileUpload={handleFileUpload}
          onClearFile={handleClearFile}
          fileInputRef={fileInputRef}
        />

        {/* Formula Selection and Options */}
        {excelData.length > 0 && (
          <FormulaSelector
            selectedFormula={selectedFormula}
            formulaOptions={formulaOptions}
            availableExcelColumns={availableExcelColumns}
            selectedFormulaDescription={selectedFormulaDescription}
            onFormulaChange={setSelectedFormula}
            onFormulaOptionChange={handleFormulaOptionChange}
            onPointFactorColumnsChange={handlePointFactorColumnsChange}
          />
        )}

        {/* Global Constraints Section */}
        {excelData.length > 0 && (
          <GlobalConstraints
            formulaOptions={formulaOptions}
            onFormulaOptionChange={handleFormulaOptionChange}
          />
        )}

        {/* Display Results Section */}
        {calculatedTips.length > 0 && (
          <div className="mt-8 space-y-6">
            <AnalyticsPanel
              showAnalytics={showAnalytics}
              onToggleAnalytics={() => setShowAnalytics(!showAnalytics)}
              calculationSummary={calculationSummary}
              fairnessMetrics={fairnessMetrics}
              dataValidation={dataValidation}
            />

            <ResultsTable
              results={calculatedTips}
              onDownloadExcel={exportToExcel}
            />
          </div>
        )}

        {/* Instructions/Placeholder for more features */}
        {calculatedTips.length === 0 && !isProcessing && (
          <InstructionsPanel />
        )}
      </div>
    </div>
  );
}
