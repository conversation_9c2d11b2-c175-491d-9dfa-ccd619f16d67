import React from 'react';
import { useTranslations } from 'next-intl';
import { TipCalculationSummary, FairnessMetrics, DataValidationResult } from '../lib/utils/tipCalculationHelper';

interface AnalyticsPanelProps {
  showAnalytics: boolean;
  onToggleAnalytics: () => void;
  calculationSummary: TipCalculationSummary | null;
  fairnessMetrics: FairnessMetrics | null;
  dataValidation: DataValidationResult | null;
}

const AnalyticsPanel: React.FC<AnalyticsPanelProps> = ({
  showAnalytics,
  onToggleAnalytics,
  calculationSummary,
  fairnessMetrics,
  dataValidation
}) => {
  const t = useTranslations('home');

  return (
    <div className="space-y-6">
      {/* Analytics Toggle */}
      <div className="flex justify-center">
        <button
          onClick={onToggleAnalytics}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-150 ease-in-out"
        >
          {showAnalytics ? t('results.hideAnalytics') : t('results.showAnalytics')}
        </button>
      </div>

      {/* Analytics Section */}
      {showAnalytics && calculationSummary && fairnessMetrics && (
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-bold text-indigo-800 mb-4 text-center">{t('results.analytics.title')}</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Summary Cards */}
            <div className="bg-white p-4 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-1">{t('results.analytics.totalTipPool')}</h4>
              <p className="text-2xl font-bold text-green-600">${calculationSummary.totalTipPool.toFixed(2)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-1">{t('results.analytics.averageTip')}</h4>
              <p className="text-2xl font-bold text-blue-600">${calculationSummary.averageTip.toFixed(2)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-1">{t('results.analytics.medianTip')}</h4>
              <p className="text-2xl font-bold text-purple-600">${calculationSummary.medianTip.toFixed(2)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-1">{t('results.analytics.tipRange')}</h4>
              <p className="text-lg font-bold text-orange-600">
                ${calculationSummary.tipRange.min.toFixed(2)} - ${calculationSummary.tipRange.max.toFixed(2)}
              </p>
            </div>
          </div>

          {/* Fairness Metrics */}
          <div className="bg-white p-4 rounded-lg shadow mb-4">
            <h4 className="text-lg font-semibold text-gray-800 mb-3">{t('results.analytics.fairnessAnalysis')}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-600">{t('results.analytics.equalityScore')}</span>
                <div className="flex items-center mt-1">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${fairnessMetrics.giniCoefficient < 0.3 ? 'bg-green-500' : fairnessMetrics.giniCoefficient < 0.5 ? 'bg-yellow-500' : 'bg-red-500'}`}
                      style={{ width: `${Math.min(fairnessMetrics.giniCoefficient * 100, 100)}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium">
                    {fairnessMetrics.giniCoefficient < 0.3 ? t('results.analytics.veryFair') : fairnessMetrics.giniCoefficient < 0.5 ? t('results.analytics.moderatelyFair') : t('results.analytics.lessFair')}
                  </span>
                </div>
              </div>
              <div>
                <span className="text-sm text-gray-600">{t('results.analytics.tipPerHourVariance')}</span>
                <p className="text-lg font-medium">${fairnessMetrics.tipPerHourVariance.toFixed(2)}</p>
              </div>
            </div>
          </div>

          {/* Data Validation Warnings */}
          {dataValidation && dataValidation.warnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
              <h4 className="text-lg font-semibold text-yellow-800 mb-2">{t('results.analytics.dataQualityWarnings')}</h4>
              <ul className="list-disc list-inside text-sm text-yellow-700">
                {dataValidation.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AnalyticsPanel;
