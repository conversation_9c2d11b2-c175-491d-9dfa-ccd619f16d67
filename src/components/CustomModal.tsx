import React from 'react';
import { useTranslations } from 'next-intl';

interface CustomModalProps {
  message: string;
  onClose: () => void;
  show: boolean;
}

const CustomModal: React.FC<CustomModalProps> = ({ message, onClose, show }) => {
  const t = useTranslations('common');
  
  if (!show) return null;
  
  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-center z-50 p-4">
      <div className="bg-white p-6 rounded-xl shadow-2xl max-w-sm w-full text-center border border-gray-200">
        <p className="text-lg font-semibold mb-4 text-gray-800">{message}</p>
        <button
          onClick={onClose}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out shadow-md"
        >
          {t('ok')}
        </button>
      </div>
    </div>
  );
};

export default CustomModal;
