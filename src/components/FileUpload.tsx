import React, { ChangeEvent, RefObject } from 'react';
import { useTranslations } from 'next-intl';

interface FileUploadProps {
  fileName: string;
  isProcessing: boolean;
  errorMessage: string;
  fileInputRef?: RefObject<HTMLInputElement | null>;
  onFileUpload: (event: ChangeEvent<HTMLInputElement>) => void;
  onClearFile: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({
  fileName,
  isProcessing,
  errorMessage,
  fileInputRef,
  onFileUpload,
  onClearFile
}) => {
  const t = useTranslations('home');
  const tCommon = useTranslations('common');

  return (
    <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg shadow-sm flex flex-col items-center">
      <label htmlFor="file-upload" className="block text-lg font-semibold text-blue-800 mb-4">
        {t('fileUpload.title')}
      </label>
      
      {!fileName && (
        <input
          id="file-upload"
          type="file"
          accept=".xlsx, .xls"
          onChange={onFileUpload}
          ref={fileInputRef}
          className="block w-full text-sm text-gray-500
                     file:mr-4 file:py-2 file:px-4
                     file:rounded-full file:border-0
                     file:text-sm file:font-semibold
                     file:bg-blue-100 file:text-blue-700
                     hover:file:bg-blue-200 cursor-pointer"
        />
      )}
      
      <div className="flex flex-col sm:flex-row items-center w-full gap-3 justify-center place-items-center">
        {fileName && (
          <p className="mt-2 text-sm text-gray-600">
            {t('fileUpload.selectedFile')} <span className="font-medium">{fileName}</span>
          </p>
        )}
        
        {fileName && (
          <svg 
            className="w-5 h-5 cursor-pointer" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            viewBox="0 0 24 24" 
            onClick={onClearFile}
          >
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="currentColor" className="text-red-500" />
            <path stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M15 9l-6 6M9 9l6 6" />
          </svg>
        )}
      </div>
      
      {isProcessing && (
        <div className="mt-4 flex items-center text-blue-600">
          <svg className="animate-spin h-5 w-5 mr-3 text-blue-500" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {tCommon('processing')}
        </div>
      )}
      
      {errorMessage && (
        <p className="mt-4 text-red-600 text-sm font-medium">{errorMessage}</p>
      )}
    </div>
  );
};

export default FileUpload;
