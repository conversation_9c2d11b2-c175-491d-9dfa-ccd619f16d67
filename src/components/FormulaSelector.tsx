import React, { ChangeEvent } from 'react';
import { useTranslations } from 'next-intl';
import { FormulaOptions } from '../lib/utils/tipCalculationHelper';

interface FormulaSelectorProps {
  selectedFormula: string;
  formulaOptions: FormulaOptions;
  availableExcelColumns: string[];
  selectedFormulaDescription: string;
  onFormulaChange: (formula: string) => void;
  onFormulaOptionChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  onPointFactorColumnsChange: (e: ChangeEvent<HTMLSelectElement>) => void;
}

const FormulaSelector: React.FC<FormulaSelectorProps> = ({
  selectedFormula,
  formulaOptions,
  availableExcelColumns,
  selectedFormulaDescription,
  onFormulaChange,
  onFormulaOptionChange,
  onPointFactorColumnsChange
}) => {
  const t = useTranslations('home');

  return (
    <div className="mb-8 p-6 bg-gray-100 border border-gray-200 rounded-lg shadow-sm">
      <h2 className="text-xl font-bold text-gray-800 mb-4">{t('calculations.title')}</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Formula Type Selection */}
        <div>
          <label htmlFor="formula-select" className="block text-sm font-medium text-gray-700 mb-2">
            {t('calculations.selectFormula')}
          </label>
          <select
            id="formula-select"
            value={selectedFormula}
            onChange={(e) => onFormulaChange(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md shadow-sm"
          >
            <optgroup label={t('calculations.basicMethods')}>
              <option value="proportionalByHours">{t('calculations.proportionalByHours')}</option>
              <option value="fixedAmount">{t('calculations.fixedAmount')}</option>
              <option value="pointsSystem">{t('calculations.pointsSystem')}</option>
              <option value="directPercentage">{t('calculations.directPercentage')}</option>
            </optgroup>
            <optgroup label={t('calculations.enhancedMethods')}>
              <option value="weightedDistribution">{t('calculations.weightedDistribution')}</option>
              <option value="roleBasedTips">{t('calculations.roleBasedTips')}</option>
              <option value="shiftBasedTips">{t('calculations.shiftBasedTips')}</option>
              <option value="performanceBasedTips">{t('calculations.performanceBasedTips')}</option>
              <option value="sectionBasedTips">{t('calculations.sectionBasedTips')}</option>
              <option value="serviceRatingBasedTips">{t('calculations.serviceRatingBasedTips')}</option>
            </optgroup>
          </select>
        </div>

        {/* Dynamic Formula Options */}
        <div className="p-4 border border-gray-300 rounded-md bg-white">
          <h3 className="text-md font-semibold text-gray-800 mb-3">{t('calculations.formulaOptions')}</h3>
          
          {selectedFormula === 'proportionalByHours' && (
            <div>
              <label htmlFor="tipPercentage" className="block text-sm font-medium text-gray-700 mb-1">
                {t('options.tipPoolPercentage')}
              </label>
              <input
                type="number"
                id="tipPercentage"
                name="tipPercentage"
                value={formulaOptions.tipPercentage ?? ''}
                onChange={onFormulaOptionChange}
                step="0.01"
                min="0"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          )}

          {selectedFormula === 'fixedAmount' && (
            <div>
              <label htmlFor="fixedAmountPerEmployee" className="block text-sm font-medium text-gray-700 mb-1">
                {t('options.fixedAmountPerEmployee')}
              </label>
              <input
                type="number"
                id="fixedAmountPerEmployee"
                name="fixedAmountPerEmployee"
                value={formulaOptions.fixedAmountPerEmployee ?? ''}
                onChange={onFormulaOptionChange}
                step="0.01"
                min="0"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          )}

          {selectedFormula === 'pointsSystem' && (
            <div>
              <label htmlFor="totalTipPoolForPoints" className="block text-sm font-medium text-gray-700 mb-1">
                {t('options.totalTipPool')}
              </label>
              <input
                type="number"
                id="totalTipPoolForPoints"
                name="totalTipPool"
                value={formulaOptions.totalTipPool ?? ''}
                onChange={onFormulaOptionChange}
                step="0.01"
                min="0"
                placeholder="e.g., 1000 or leave blank to calculate from sales"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm mb-3"
              />

              <label htmlFor="pointFactorColumns" className="block text-sm font-medium text-gray-700 mb-1">
                {t('options.pointFactorColumns')}
              </label>
              <select
                id="pointFactorColumns"
                name="pointFactorColumns"
                multiple
                value={formulaOptions.pointFactorColumns || []}
                onChange={onPointFactorColumnsChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md shadow-sm h-32 overflow-y-auto"
              >
                {availableExcelColumns.length > 0 ? (
                  availableExcelColumns.map(col => (
                    <option key={col} value={col}>{col}</option>
                  ))
                ) : (
                  <option disabled>{t('options.uploadExcelNote')}</option>
                )}
              </select>
              <p className="mt-2 text-xs text-gray-500">
                {t('options.pointFactorColumnsNote')}
              </p>
            </div>
          )}

          {selectedFormula === 'directPercentage' && (
            <div>
              <label htmlFor="tipPercentageDirect" className="block text-sm font-medium text-gray-700 mb-1">
                {t('options.directPercentage')}
              </label>
              <input
                type="number"
                id="tipPercentageDirect"
                name="tipPercentage"
                value={formulaOptions.tipPercentage ?? ''}
                onChange={onFormulaOptionChange}
                step="0.01"
                min="0"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          )}

          {selectedFormula === 'weightedDistribution' && (
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label htmlFor="hoursWeight" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('options.hoursWeight')}
                  </label>
                  <input
                    type="number"
                    id="hoursWeight"
                    name="hoursWeight"
                    value={formulaOptions.hoursWeight ?? ''}
                    onChange={onFormulaOptionChange}
                    step="0.1"
                    min="0"
                    max="1"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="salesWeight" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('options.salesWeight')}
                  </label>
                  <input
                    type="number"
                    id="salesWeight"
                    name="salesWeight"
                    value={formulaOptions.salesWeight ?? ''}
                    onChange={onFormulaOptionChange}
                    step="0.1"
                    min="0"
                    max="1"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="performanceWeight" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('options.performanceWeight')}
                  </label>
                  <input
                    type="number"
                    id="performanceWeight"
                    name="performanceWeight"
                    value={formulaOptions.performanceWeight ?? ''}
                    onChange={onFormulaOptionChange}
                    step="0.1"
                    min="0"
                    max="1"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="performanceColumn" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('options.performanceColumn')}
                </label>
                <select
                  id="performanceColumn"
                  name="performanceColumn"
                  value={formulaOptions.performanceColumn ?? ''}
                  onChange={onFormulaOptionChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">{t('options.selectPerformanceColumn')}</option>
                  {availableExcelColumns.map(col => (
                    <option key={col} value={col}>{col}</option>
                  ))}
                </select>
              </div>
              <p className="text-xs text-gray-600">
                {t('options.weightsNote')}
              </p>
            </div>
          )}

          {selectedFormula === 'roleBasedTips' && (
            <div>
              <label htmlFor="roleColumn" className="block text-sm font-medium text-gray-700 mb-1">
                {t('options.roleColumn')}
              </label>
              <select
                id="roleColumn"
                name="roleColumn"
                value={formulaOptions.roleColumn ?? ''}
                onChange={onFormulaOptionChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">{t('options.selectRoleColumn')}</option>
                {availableExcelColumns.map(col => (
                  <option key={col} value={col}>{col}</option>
                ))}
              </select>
              <p className="text-xs text-gray-600 mt-2">
                {t('options.roleMultipliersNote')}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Help Text Section */}
      {selectedFormulaDescription && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-md font-semibold text-blue-800 mb-3">{t('calculations.helpText')}</h3>
          <pre className="text-sm text-gray-700 whitespace-pre-wrap">{selectedFormulaDescription}</pre>
        </div>
      )}
    </div>
  );
};

export default FormulaSelector;
