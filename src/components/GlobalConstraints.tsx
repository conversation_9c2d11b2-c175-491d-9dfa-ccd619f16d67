import React, { ChangeEvent } from 'react';
import { useTranslations } from 'next-intl';
import { FormulaOptions } from '../lib/utils/tipCalculationHelper';

interface GlobalConstraintsProps {
  formulaOptions: FormulaOptions;
  onFormulaOptionChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
}

const GlobalConstraints: React.FC<GlobalConstraintsProps> = ({
  formulaOptions,
  onFormulaOptionChange
}) => {
  const t = useTranslations('home');

  return (
    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 className="text-md font-semibold text-blue-800 mb-3">{t('constraints.title')}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label htmlFor="minimumTipPerEmployee" className="block text-sm font-medium text-gray-700 mb-1">
            {t('constraints.minimumTipPerEmployee')}
          </label>
          <input
            type="number"
            id="minimumTipPerEmployee"
            name="minimumTipPerEmployee"
            value={formulaOptions.minimumTipPerEmployee ?? ''}
            onChange={onFormulaOptionChange}
            step="0.01"
            min="0"
            placeholder={t('constraints.noMinimum')}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <label htmlFor="maximumTipPerEmployee" className="block text-sm font-medium text-gray-700 mb-1">
            {t('constraints.maximumTipPerEmployee')}
          </label>
          <input
            type="number"
            id="maximumTipPerEmployee"
            name="maximumTipPerEmployee"
            value={formulaOptions.maximumTipPerEmployee ?? ''}
            onChange={onFormulaOptionChange}
            step="0.01"
            min="0"
            placeholder={t('constraints.noMaximum')}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <label htmlFor="minimumTipPerHour" className="block text-sm font-medium text-gray-700 mb-1">
            {t('constraints.minimumTipPerHour')}
          </label>
          <input
            type="number"
            id="minimumTipPerHour"
            name="minimumTipPerHour"
            value={formulaOptions.minimumTipPerHour ?? ''}
            onChange={onFormulaOptionChange}
            step="0.01"
            min="0"
            placeholder={t('constraints.noMinimum')}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>
      
      <p className="text-xs text-gray-600 mt-2">
        {t('constraints.constraintsNote')}
      </p>
    </div>
  );
};

export default GlobalConstraints;
