import React from 'react';
import { useTranslations } from 'next-intl';

const InstructionsPanel: React.FC = () => {
  const t = useTranslations('home');

  return (
    <div className="text-center py-12">
      <div className="max-w-md mx-auto">
        <div className="mb-8">
          <svg
            className="mx-auto h-24 w-24 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t('instructions.title')}
        </h3>
        <p className="text-gray-500 text-sm mb-6">
          {t('instructions.description')}
        </p>
        <div className="text-left space-y-3">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 text-xs font-medium">
                1
              </div>
            </div>
            <p className="ml-3 text-sm text-gray-600">
              {t('instructions.step1')}
            </p>
          </div>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 text-xs font-medium">
                2
              </div>
            </div>
            <p className="ml-3 text-sm text-gray-600">
              {t('instructions.step2')}
            </p>
          </div>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 text-xs font-medium">
                3
              </div>
            </div>
            <p className="ml-3 text-sm text-gray-600">
              {t('instructions.step3')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstructionsPanel;
