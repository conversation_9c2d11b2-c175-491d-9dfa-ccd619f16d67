import React from 'react';
import { useTranslations } from 'next-intl';
import { CalculatedTipResult } from '../lib/utils/tipCalculationHelper';

interface ResultsTableProps {
  results: CalculatedTipResult[];
  onDownloadExcel: () => void;
}

const ResultsTable: React.FC<ResultsTableProps> = ({
  results,
  onDownloadExcel
}) => {
  const t = useTranslations('home');

  if (results.length === 0) {
    return null;
  }

  return (
    <div className="mt-6">
      <div className="p-6 bg-white border border-gray-200 rounded-xl shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-800">{t('results.title')}</h2>
          <button
            onClick={onDownloadExcel}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-150 ease-in-out"
          >
            {t('results.downloadExcel')}
          </button>
        </div>
        
        <div className="overflow-x-auto rounded-lg border border-gray-300 shadow-inner">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-100">
              <tr>
                {Object.keys(results[0]).map((header, index) => (
                  <th
                    key={index}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {results.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50">
                  {Object.values(row).map((value, colIndex) => (
                    <td
                      key={colIndex}
                      className="px-6 py-4 whitespace-nowrap text-sm text-gray-800"
                    >
                      {String(value)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ResultsTable;
