import { FormulaOptions } from '../lib/utils/tipCalculationHelper';

export const FORMULA_OPTIONS: FormulaOptions = {
  tipPercentage: 0.05,
  fixedAmountPerEmployee: 100,
  pointsColumn: 'Points',
  pointFactorColumns: [],

  hoursWeight: 0.4,
  salesWeight: 0.4,
  performanceWeight: 0.2,
  performanceColumn: '',

  roleTiers: {
    Manager: { multiplier: 1.5, minimumTip: 50, maximumTip: 200 },
    Server: { multiplier: 1.0, minimumTip: 20, maximumTip: 150 },
    Busser: { multiplier: 0.8, minimumTip: 15, maximumTip: 100 },
    Host: { multiplier: 0.7, minimumTip: 10, maximumTip: 80 },
  },
  roleColumn: '',

  shiftMultipliers: {
    morning: 0.8,
    lunch: 1.0,
    dinner: 1.3,
    late: 1.1,
  },
  shiftColumn: '',

  performanceThresholds: {
    excellent: { threshold: 1000, bonus: 0.2 },
    good: { threshold: 500, bonus: 0.1 },
  },
  performanceMetric: 'sales',

  minimumTipPerEmployee: 0,
  maximumTipPerEmployee: 0,
  minimumTipPerHour: 0,

  sectionColumn: '',
  sectionAllocations: {
    'Dining Room': 0.6,
    Bar: 0.3,
    Kitchen: 0.1,
  },

  serviceRatingColumn: '',
  serviceRatingWeight: 0.3,
  minimumRatingThreshold: 3.0,
};
