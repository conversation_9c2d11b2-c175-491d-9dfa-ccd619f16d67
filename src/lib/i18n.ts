import { useTranslations } from 'next-intl';

// Define the IntlMessages type
type IntlMessages = Record<string, string | { [key: string]: string }>;

// Custom hook to provide type-safe translations
export function useTypedTranslations<T extends keyof IntlMessages>(namespace: T & string) {
  return useTranslations(namespace);
}

// Helper function to get all available locales
export const locales = ['en', 'fr'] as const;
export type Locale = (typeof locales)[number];

// Helper function to validate locale
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}
