import * as XLSX from 'xlsx';
import {
  calculateTipsWithAnalytics,
  EmployeeData,
  FormulaOptions,
  CalculatedTipResult,
  TipCalculationSummary,
  FairnessMetrics,
  DataValidationResult,
} from './tipCalculationHelper';

export interface FileProcessingResult {
  success: boolean;
  data?: EmployeeData[];
  columns?: string[];
  error?: string;
}

export interface TipCalculationResult {
  results: CalculatedTipResult[];
  summary: TipCalculationSummary | null;
  fairness: FairnessMetrics | null;
  validation: DataValidationResult | null;
  error?: string;
}

/**
 * Process uploaded Excel file and extract employee data
 */
export const processExcelFile = (file: File): Promise<FileProcessingResult> => {
  return new Promise((resolve) => {
    const reader = new FileReader();

    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        const data = e.target?.result;
        if (!data) {
          resolve({ success: false, error: 'No file data found' });
          return;
        }

        const workbook = XLSX.read(data, { type: 'array' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const json: (string | number)[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        const headers = json[0] as string[];
        const rows = json.slice(1);

        const parsedData: EmployeeData[] = rows.map((row) => {
          return {
            'Employee Name': row[0] as string,
            'Hours Worked': parseFloat(row[1] as string),
            'Sales Generated': parseFloat(row[2] as string),
          };
        });

        resolve({
          success: true,
          data: parsedData,
          columns: headers,
        });
      } catch (error) {
        console.error('Error reading Excel file:', error);
        resolve({
          success: false,
          error: 'Invalid file format or corrupted data',
        });
      }
    };

    reader.onerror = () => {
      resolve({
        success: false,
        error: 'File reading failed',
      });
    };

    reader.readAsArrayBuffer(file);
  });
};

/**
 * Calculate tips for employee data using specified formula and options
 */
export const calculateTipsForEmployees = (
  data: EmployeeData[],
  formula: string,
  options: FormulaOptions,
  availableColumns: string[],
): TipCalculationResult => {
  if (data.length === 0) {
    return {
      results: [],
      summary: null,
      fairness: null,
      validation: null,
    };
  }

  try {
    // Validate points system requirements
    if (formula === 'pointsSystem') {
      if (!options.pointFactorColumns || options.pointFactorColumns.length === 0) {
        throw new Error('Points system requires point factor columns to be selected');
      }

      const missingColumns = options.pointFactorColumns.filter(
        (col) => !availableColumns.includes(col),
      );
      if (missingColumns.length > 0) {
        throw new Error(`Missing required columns for points system: ${missingColumns.join(', ')}`);
      }
    }

    const enhancedResults = calculateTipsWithAnalytics(data, formula, options);

    return {
      results: enhancedResults.results,
      summary: enhancedResults.summary,
      fairness: enhancedResults.fairness,
      validation: enhancedResults.validation,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown calculation error';
    return {
      results: [],
      summary: null,
      fairness: null,
      validation: null,
      error: errorMessage,
    };
  }
};

/**
 * Export calculated tips to Excel file
 */
export const exportTipsToExcel = (results: CalculatedTipResult[], filename?: string): boolean => {
  if (results.length === 0) {
    return false;
  }

  try {
    const ws = XLSX.utils.json_to_sheet(results);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Calculated Tips');

    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });

    const downloadLink = document.createElement('a');
    const url = URL.createObjectURL(blob);
    downloadLink.href = url;
    downloadLink.download =
      filename || `tip-calculations-${new Date().toISOString().split('T')[0]}.xlsx`;

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    URL.revokeObjectURL(url);
    return true;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    return false;
  }
};

/**
 * Reset formula options to default values
 */
export const getDefaultFormulaOptions = (): FormulaOptions => {
  return {
    tipPercentage: 0.05,
    fixedAmountPerEmployee: 100,
    pointsColumn: 'Points',
    pointFactorColumns: [],

    hoursWeight: 0.4,
    salesWeight: 0.4,
    performanceWeight: 0.2,
    performanceColumn: '',

    roleTiers: {
      Manager: { multiplier: 1.5, minimumTip: 50, maximumTip: 200 },
      Server: { multiplier: 1.0, minimumTip: 20, maximumTip: 150 },
      Busser: { multiplier: 0.8, minimumTip: 15, maximumTip: 100 },
      Host: { multiplier: 0.7, minimumTip: 10, maximumTip: 80 },
    },
    roleColumn: '',

    shiftMultipliers: {
      morning: 0.8,
      lunch: 1.0,
      dinner: 1.3,
      late: 1.1,
    },
    shiftColumn: '',

    performanceThresholds: {
      excellent: { threshold: 1000, bonus: 0.2 },
      good: { threshold: 500, bonus: 0.1 },
    },
    performanceMetric: 'sales',

    minimumTipPerEmployee: 0,
    maximumTipPerEmployee: 0,
    minimumTipPerHour: 0,

    sectionColumn: '',
    sectionAllocations: {
      'Dining Room': 0.6,
      Bar: 0.3,
      Kitchen: 0.1,
    },

    serviceRatingColumn: '',
    serviceRatingWeight: 0.3,
    minimumRatingThreshold: 3.0,
  };
};
