{"methods": {"proportionalByHours": {"description": "Distributes tips proportionally based on hours worked. The total tip pool is divided among employees based on the ratio of their hours worked to the total hours worked by all employees.", "usage": {"configuration": {"tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%).", "totalTipPool": "Explicitly provided total tip pool amount.", "minimumTipPerEmployee": "Minimum tip amount guaranteed per employee.", "maximumTipPerEmployee": "Maximum tip amount allowed per employee.", "minimumTipPerHour": "Minimum tip amount guaranteed per hour worked."}, "globalConstraints": "Ensure that the total hours worked and total sales are correctly provided in the data."}}, "fixedAmount": {"description": "Distributes a fixed amount of tips to each eligible employee. Each employee receives the same fixed tip amount.", "usage": {"configuration": {"fixedAmountPerEmployee": "The fixed tip amount to be distributed to each employee.", "minimumTipPerEmployee": "Minimum tip amount guaranteed per employee.", "maximumTipPerEmployee": "Maximum tip amount allowed per employee."}, "globalConstraints": "Ensure that the fixed amount per employee is valid and greater than zero."}}, "pointsSystem": {"description": "Distributes tips based on a point system. Employees earn points based on specific factors, and tips are distributed proportionally to the points earned.", "usage": {"configuration": {"pointsColumn": "Column name for points assigned to employees.", "pointFactorColumns": "Array of column names whose values sum up to total points.", "totalTipPool": "Explicitly provided total tip pool amount.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that the points column or point factor columns exist in the data."}}, "directPercentage": {"description": "Each employee receives a direct percentage of their own sales as tips.", "usage": {"configuration": {"tipPercentage": "Percentage of each employee's sales to be distributed as tips.", "minimumTipPerEmployee": "Minimum tip amount guaranteed per employee.", "maximumTipPerEmployee": "Maximum tip amount allowed per employee."}, "globalConstraints": "Ensure that sales data is correctly provided for each employee."}}, "weightedDistribution": {"description": "Distributes tips using a weighted system combining hours worked, sales generated, and performance metrics. Each factor is assigned a weight, and tips are distributed based on the weighted scores.", "usage": {"configuration": {"hoursWeight": "Weight for hours worked (0-1).", "salesWeight": "Weight for sales generated (0-1).", "performanceWeight": "Weight for performance metrics (0-1).", "performanceColumn": "Column name for performance data.", "totalTipPool": "Explicitly provided total tip pool amount.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that weights sum to 1 and that data for all weighted factors is correctly provided."}}, "roleBasedTips": {"description": "Distributes tips based on employee roles. Each role is assigned a multiplier, and tips are distributed proportionally to the weighted hours worked by employees in each role.", "usage": {"configuration": {"roleColumn": "Column name for employee roles.", "roleTiers": "Object defining multipliers and optional min/max tips for each role.", "totalTipPool": "Explicitly provided total tip pool amount.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that the role column and role tiers are correctly defined."}}, "shiftBasedTips": {"description": "Distributes tips based on shifts worked. Each shift is assigned a multiplier, and tips are distributed proportionally to the weighted hours worked during each shift.", "usage": {"configuration": {"shiftColumn": "Column name for shift information.", "shiftMultipliers": "Object defining multipliers for each shift.", "totalTipPool": "Explicitly provided total tip pool amount.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that the shift column and shift multipliers are correctly defined."}}, "performanceBasedTips": {"description": "Distributes tips based on performance metrics. Employees meeting specific performance thresholds receive bonuses.", "usage": {"configuration": {"performanceThresholds": "Object defining thresholds and bonuses for performance levels.", "performanceMetric": "Metric used to evaluate performance (e.g., 'sales', 'efficiency').", "totalTipPool": "Explicitly provided total tip pool amount.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that performance data and thresholds are correctly defined."}}, "sectionBasedTips": {"description": "Distributes tips based on sections or areas worked. Each section (e.g., 'A', 'Bar', 'Front') can have its own allocation (fixed amount or percentage) or multiplier. Tips are distributed according to section assignments, allowing for custom pooling by area. Example: sectionAllocations = { 'A': 500, 'Bar': 300, 'Front': 200 } will allocate $500 to section A, $300 to Bar, and $200 to Front.", "usage": {"configuration": {"sectionColumn": "Column name for section information (e.g., 'Section').", "sectionAllocations": "Object defining tip allocations or multipliers for each section. Example: { 'A': 500, 'Bar': 300 }.", "totalTipPool": "Explicitly provided total tip pool amount. If not provided, calculated from sales and tipPercentage.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that the section column exists in the data and sectionAllocations covers all relevant sections. If an employee's section is missing from sectionAllocations, their tip may be zero."}}, "serviceRatingBasedTips": {"description": "Distributes tips based on customer service ratings. Employees with higher service ratings (e.g., 4.5+) can receive a greater share of the tip pool, or minimum rating thresholds can be enforced to exclude low performers. The serviceRatingWeight determines how much the rating influences the tip calculation. Example: serviceRatingWeight = 0.3 means 30% of the tip is based on service rating, 70% on other factors.", "usage": {"configuration": {"serviceRatingColumn": "Column name for service rating information (e.g., 'Service Rating').", "serviceRatingWeight": "Weight for service rating in tip calculation (0-1). Example: 0.3 means 30% of tip is based on rating.", "minimumRatingThreshold": "Minimum rating required to be eligible for tips. Example: 4.0 means only employees rated 4.0 or higher receive tips.", "totalTipPool": "Explicitly provided total tip pool amount. If not provided, calculated from sales and tipPercentage.", "tipPercentage": "Percentage of total sales to form the tip pool (e.g., 0.05 for 5%)."}, "globalConstraints": "Ensure that the service rating column exists and ratings are numeric. If minimumRatingThreshold is set, employees below this rating will not receive tips."}}}}